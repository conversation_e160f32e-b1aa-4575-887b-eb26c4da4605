import dynamic from "next/dynamic";
import { useEffect, useState } from "react";
import { LoadingScreen } from "@/components/three/ui/LoadingScreen";

const GPUDetector = dynamic(
  () =>
    Promise.resolve(
      ({ children }: { children: (tier: number) => React.ReactNode }) => {
        const [tier, setTier] = useState(2);
        const [loading, setLoading] = useState(true);

        useEffect(() => {
          let isMounted = true;

          import("detect-gpu").then(({ getGPUTier }) => {
            getGPUTier()
              .then((result) => {
                if (isMounted) {
                  setTier(2);
                  setLoading(false);
                }
              })
              .catch(() => {
                if (isMounted) {
                  setTier(2);
                  setLoading(false);
                }
              });
          });

          return () => {
            isMounted = false;
          };
        }, []);

        if (loading) {
          return <LoadingScreen />;
        }

        return <>{children(tier)}</>;
      }
    ),
  { ssr: false, loading: () => <LoadingScreen /> }
);

export default GPUDetector;
